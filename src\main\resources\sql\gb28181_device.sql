-- GB28181设备表创建脚本
-- 用于存储GB28181协议的摄像机设备信息

-- 创建gb28181_device表
DROP TABLE IF EXISTS `gb28181_device`;
CREATE TABLE `gb28181_device` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备ID，20位数字，符合GB28181规范',
  `device_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备名称',
  `manufacturer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备厂商',
  `model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备型号',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'offline' COMMENT '设备状态：online在线，offline离线',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备IP地址',
  `port` int NULL DEFAULT NULL COMMENT '设备端口',
  `transport_protocol` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'UDP' COMMENT '传输协议：UDP或TCP',
  `last_register_time` datetime NULL DEFAULT NULL COMMENT '最后注册时间',
  `last_keepalive_time` datetime NULL DEFAULT NULL COMMENT '最后心跳时间',
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `server_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属服务器IP，用于分布式部署',
  `rtp_port` int NULL DEFAULT NULL COMMENT '当前分配的RTP端口',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备所在城市',
  `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备所在县区',
  `township` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备所在乡镇',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_device_id`(`device_id` ASC) USING BTREE COMMENT '设备ID唯一索引',
  INDEX `idx_status`(`status` ASC) USING BTREE COMMENT '设备状态索引',
  INDEX `idx_last_keepalive`(`last_keepalive_time` ASC) USING BTREE COMMENT '心跳时间索引，用于离线检测',
  INDEX `idx_rtp_port`(`rtp_port` ASC) USING BTREE COMMENT 'RTP端口索引，用于端口管理'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'GB28181设备信息表' ROW_FORMAT = Dynamic;

-- 如果表已存在但缺少transport_protocol字段，则添加该字段
-- ALTER TABLE `gb28181_device` ADD COLUMN `transport_protocol` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'UDP' COMMENT '传输协议：UDP或TCP' AFTER `port`;
