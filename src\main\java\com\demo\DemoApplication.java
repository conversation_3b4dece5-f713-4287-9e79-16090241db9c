package com.demo;
import cn.dev33.satoken.SaManager;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@EnableTransactionManagement
@EnableScheduling
public class DemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(DemoApplication.class, args);
        System.out.println("=================================");
        System.out.println("GB28181 SIP服务器启动完成！");
        System.out.println("SIP端口: 5060");
        System.out.println("HTTP API: http://localhost:8080/api/gb28181/device/list");
        System.out.println("=================================");
        System.out.println("启动成功，Sa-Token 配置如下：" + SaManager.getConfig());
    }

}
