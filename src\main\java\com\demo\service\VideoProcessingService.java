package com.demo.service;

import cn.dev33.satoken.util.SaResult;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 视频处理服务接口
 * 用于处理违法数据相关的视频截取和合成
 */
public interface VideoProcessingService {

    /**
     * 处理违法数据的视频截取
     * 根据违法记录的创建时间，截取前后10秒的视频
     * 
     * @param illegalRecordId 违法记录ID
     * @param createTime 违法记录创建时间
     * @param equipmentNumber 设备编号
     * @return 处理结果
     */
    SaResult processIllegalVideo(String illegalRecordId, LocalDateTime createTime, String equipmentNumber);

    /**
     * 截取单个视频文件的指定时间段
     * 
     * @param videoFilePath 视频文件路径
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param outputPath 输出文件路径
     * @return 截取结果
     */
    SaResult extractVideoSegment(String videoFilePath, LocalDateTime startTime, LocalDateTime endTime, String outputPath);

    /**
     * 合成多个视频片段
     * 
     * @param videoSegments 视频片段路径列表
     * @param outputPath 输出文件路径
     * @return 合成结果
     */
    SaResult mergeVideoSegments(List<String> videoSegments, String outputPath);

    /**
     * 根据设备编号和时间范围查找对应的视频文件
     * 
     * @param equipmentNumber 设备编号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 视频文件路径列表
     */
    List<String> findVideoFiles(String equipmentNumber, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 构建视频文件存储路径
     * 
     * @param equipmentNumber 设备编号（如：c0:74:2b:fc:29:f1）
     * @param isSecondCamera 是否为第二个摄像头（true=1921680130, false=1921680120）
     * @return 构建的流名称
     */
    String buildStreamName(String equipmentNumber, boolean isSecondCamera);

    /**
     * 检查视频文件是否存在
     * 
     * @param filePath 文件路径
     * @return 是否存在
     */
    boolean isVideoFileExists(String filePath);

    /**
     * 获取视频文件的时间范围
     * 
     * @param filePath 视频文件路径
     * @return 时间范围数组 [开始时间, 结束时间]
     */
    LocalDateTime[] getVideoTimeRange(String filePath);
}
