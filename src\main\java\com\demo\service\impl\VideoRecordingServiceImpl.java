package com.demo.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.Device;
import com.demo.entity.Users;
import com.demo.entity.VideoRecording;
import com.demo.entity.DTO.DeviceTreeNode;
import com.demo.mapper.DeviceMapper;
import com.demo.mapper.UsersMapper;
import com.demo.mapper.VideoRecordingMapper;
import com.demo.service.VideoRecordingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 视频录制服务实现类
 */
@Slf4j
@Service
public class VideoRecordingServiceImpl extends ServiceImpl<VideoRecordingMapper, VideoRecording> implements VideoRecordingService {
    @Autowired
    private DeviceMapper deviceMapper;
    @Autowired
    private UsersMapper usersMapper;
    @Override
    public SaResult handleDvrCallback(Map<String, Object> payload) {
        try {
            String action = (String) payload.get("action");
            String streamName = (String) payload.get("stream");
            String filePath = (String) payload.get("file");

            log.info("收到DVR回调: action={}, stream={}, file={}", action, streamName, filePath);

            if ("on_dvr".equals(action)) {
                return handleDvrFileCreated(streamName, filePath);
            }

            return SaResult.ok("DVR回调处理完成");
        } catch (Exception e) {
            log.error("处理DVR回调异常: {}", e.getMessage(), e);
            return SaResult.error("处理DVR回调失败: " + e.getMessage());
        }
    }

    private SaResult handleDvrFileCreated(String streamName, String filePath) {
        try {
            // 处理相对路径，转换为绝对路径
            String absoluteFilePath = filePath;
            // 已经是绝对路径
            log.warn("DVR文件已转换为绝对路径: {}", absoluteFilePath);
            absoluteFilePath = filePath;
            // 解析文件信息
            File file = new File(absoluteFilePath);
            if (!file.exists()) {
                log.warn("DVR文件不存在: {} (绝对路径: {})", filePath, absoluteFilePath);
                return SaResult.error("文件不存在");
            }
            // 解析录制开始时间
            LocalDateTime startTime = parseRecordingStartTime(absoluteFilePath);
            // 使用ffprobe获取视频实际时长，计算结束时间
            LocalDateTime endTime = calculateVideoEndTime(absoluteFilePath, startTime);
            // 创建录制记录
            VideoRecording recording = new VideoRecording();
            recording.setStreamName(streamName);
            recording.setFilePath(absoluteFilePath); // 使用绝对路径
            recording.setFileName(file.getName());
            recording.setFileSize(file.length());
            recording.setStartTime(startTime);
            recording.setEndTime(endTime);
            recording.setStatus(VideoRecording.STATUS_COMPLETED);
            recording.setCreatedTime(LocalDateTime.now());
            // 计算录制时长
            long durationSeconds = java.time.Duration.between(startTime, endTime).getSeconds();
            recording.setDuration((int) durationSeconds);

            // 保存到数据库
            this.save(recording);

            log.info("DVR文件记录已保存: streamName={}, fileName={}, size={}, startTime={}, endTime={}, duration={}秒",
                    streamName, recording.getFileName(), recording.getFileSize(),
                    recording.getStartTime(), recording.getEndTime(), recording.getDuration());
            return SaResult.ok("DVR文件处理完成");
        } catch (Exception e) {
            log.error("处理DVR文件异常: {}", e.getMessage(), e);
            return SaResult.error("处理DVR文件失败: " + e.getMessage());
        }
    }

    @Override
    public SaResult getRecordingsList(String streamName, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<VideoRecording> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VideoRecording::getStreamName, streamName)
                .eq(VideoRecording::getStatus, VideoRecording.STATUS_COMPLETED)
                .between(VideoRecording::getStartTime, startTime, endTime);
        List<VideoRecording> recordings = this.list(queryWrapper);
        return SaResult.data(recordings);
    }

    @Override
    public SaResult getCameraTree() {
        try {
            List<String> roleList = StpUtil.getRoleList(); // 获取：当前账号的角色集合
            int loginIdAsInt = StpUtil.getLoginIdAsInt(); // 获取当前会话账号id, 并转换为int类型
            Users users = usersMapper.selectById(loginIdAsInt);

            // 获取用户有权限访问的设备列表
            List<Device> devices = determineTheDeviceTreeData(roleList, users);

            if (devices.isEmpty()) {
                log.info("用户{}没有可访问的设备", loginIdAsInt);
                return SaResult.data(Collections.emptyList());
            }

            // 构建设备树形结构
            List<DeviceTreeNode> deviceTree = buildDeviceTree(devices, roleList, users);
            return SaResult.data(deviceTree);
        } catch (Exception e) {
            log.error("构建设备树失败", e);
            return SaResult.error("获取设备树失败：" + e.getMessage());
        }
    }

    private List<Device> determineTheDeviceTreeData(List<String> roleList, Users user) {
        if (roleList == null || user == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getDeviceType, "摄像机");
        if (roleList.contains("city") && StringUtils.isNotBlank(user.getCity())) {
            queryWrapper.eq(Device::getCity, user.getCity());
        } else if (roleList.contains("county") && StringUtils.isNotBlank(user.getCounty())) {
            queryWrapper.eq(Device::getCity, user.getCity())
                    .eq(Device::getCounty, user.getCounty());
        } else if (roleList.contains("township") && StringUtils.isNotBlank(user.getTownship())) {
            queryWrapper.eq(Device::getCity, user.getCity())
                    .eq(Device::getCounty, user.getCounty())
                    .eq(Device::getTownship, user.getTownship());
        } else if (roleList.contains("hamlet") && StringUtils.isNotBlank(user.getHamlet())) {
            queryWrapper.eq(Device::getCity, user.getCity())
                    .eq(Device::getCounty, user.getCounty())
                    .eq(Device::getTownship, user.getTownship())
                    .eq(Device::getHamlet, user.getHamlet());
        } else if (roleList.contains("site") && StringUtils.isNotBlank(user.getSite())) {
            queryWrapper.eq(Device::getCity, user.getCity())
                    .eq(Device::getCounty, user.getCounty())
                    .eq(Device::getTownship, user.getTownship())
                    .eq(Device::getHamlet, user.getHamlet())
                    .eq(Device::getSite, user.getSite());
        }
        List<Device> result = deviceMapper.selectList(queryWrapper);
        return result != null ? result : Collections.emptyList();
    }

    /**
     * 解析录制开始时间
     * 从文件路径和文件名中解析出录制的实际开始时间
     */
    private LocalDateTime parseRecordingStartTime(String filePath) {
        try {
            log.info("开始解析录制时间，文件路径: {}", filePath);
            // 路径格式: .../2025/07/22/09.47.14.968.mp4
            String pathPattern = ".*/([0-9]{4})/([0-9]{2})/([0-9]{2})/([0-9]{2})\\.([0-9]{2})\\.([0-9]{2})\\.([0-9]{3})\\.mp4$";
            Pattern dateTimePattern = Pattern.compile(pathPattern);
            Matcher dateTimeMatcher = dateTimePattern.matcher(filePath);
            // 检查是否匹配成功
            if (!dateTimeMatcher.find()) {
                log.warn("文件路径格式不匹配正则表达式，路径: {}", filePath);
                 return LocalDateTime.now().minusMinutes(30); // 默认假设30分钟前开始录制;
            }
                int year = Integer.parseInt(dateTimeMatcher.group(1));
                int month = Integer.parseInt(dateTimeMatcher.group(2));
                int day = Integer.parseInt(dateTimeMatcher.group(3));
                int hour = Integer.parseInt(dateTimeMatcher.group(4));
                int minute = Integer.parseInt(dateTimeMatcher.group(5));
                int second = Integer.parseInt(dateTimeMatcher.group(6));
                int millisecond = Integer.parseInt(dateTimeMatcher.group(7));
                LocalDateTime timeFromPath = LocalDateTime.of(year, month, day, hour, minute, second, millisecond * 1000000);
                log.info("成功解析录制开始时间: {} (年:{} 月:{} 日:{} 时:{} 分:{} 秒:{} 毫秒:{})",
                        timeFromPath, year, month, day, hour, minute, second, millisecond);
                return timeFromPath;
        } catch (Exception e) {
            log.warn("解析录制开始时间失败，使用当前时间: {}", e.getMessage());
            return LocalDateTime.now().minusMinutes(30); // 默认假设30分钟前开始录制
        }
    }

    /**
     * 计算视频结束时间
     * 使用ffprobe获取视频实际时长，基于开始时间计算结束时间
     */
    private LocalDateTime calculateVideoEndTime(String videoFilePath, LocalDateTime startTime) {
        try {
            // 使用ffprobe获取视频时长
            List<String> command = Arrays.asList(
                "ffprobe",
                "-v", "quiet",
                "-show_entries", "format=duration",
                "-of", "csv=p=0",
                videoFilePath
            );

            ProcessBuilder pb = new ProcessBuilder(command);
            Process process = pb.start();

            StringBuilder output = new StringBuilder();
            try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line);
                }
            }

            boolean finished = process.waitFor(10, java.util.concurrent.TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                log.info("ffprobe获取视频时长超时，使用文件修改时间: {}", videoFilePath);
                return getFileModificationTime(videoFilePath);
            }

            if (process.exitValue() == 0) {
                String durationStr = output.toString().trim();
                if (!durationStr.isEmpty()) {
                    double duration = Double.parseDouble(durationStr);
                    LocalDateTime endTime = startTime.plusSeconds((long) duration);
                    log.info("通过ffprobe计算视频结束时间: 开始={}, 时长={}秒, 结束={}",
                            startTime, duration, endTime);
                    return endTime;
                }
            }

            log.info("ffprobe获取视频时长失败，使用文件修改时间: {}", videoFilePath);
            return getFileModificationTime(videoFilePath);

        } catch (Exception e) {
            log.error("计算视频结束时间时发生异常，使用文件修改时间: {}", videoFilePath, e);
            return getFileModificationTime(videoFilePath);
        }
    }

    /**
     * 获取文件修改时间（备用方案）
     */
    private LocalDateTime getFileModificationTime(String videoFilePath) {
        try {
            java.io.File file = new java.io.File(videoFilePath);
            return LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(file.lastModified()),
                java.time.ZoneId.systemDefault()
            );
        } catch (Exception e) {
            log.error("获取文件修改时间失败: {}", videoFilePath, e);
            return LocalDateTime.now();
        }
    }
    /**
     * 构建设备树形结构
     * 根据用户权限和设备数据构建层级树形结构
     */
    private List<DeviceTreeNode> buildDeviceTree(List<Device> devices, List<String> roleList, Users user) {
        if (devices.isEmpty()) {
            return Collections.emptyList();
        }
        // 确定用户权限的起始层级
        String userLevel = determineUserLevel(roleList);
        // 使用Map来存储每个层级的节点，避免重复创建
        Map<String, DeviceTreeNode> nodeMap = new HashMap<>();
        // 为每个设备构建完整的路径
        for (Device device : devices) {
            buildDevicePathInTree(device, userLevel, nodeMap);
        }
        // 找出根节点（没有父节点的节点）
        return findRootNodes(nodeMap, userLevel);
    }

    /**
     * 确定用户权限对应的地理层级
     */
    private String determineUserLevel(List<String> roleList) {
        if (roleList.contains("site")) return "site";
        if (roleList.contains("hamlet")) return "hamlet";
        if (roleList.contains("township")) return "township";
        if (roleList.contains("county")) return "county";
        if (roleList.contains("city")) return "city";
        return "city"; // 默认市级
    }

    /**
     * 为单个设备在树中构建完整路径
     */
    private void buildDevicePathInTree(Device device, String userLevel, Map<String, DeviceTreeNode> nodeMap) {
        String[] levels = {"city", "county", "township", "hamlet", "site"};
        String[] values = {device.getCity(), device.getCounty(), device.getTownship(),
                          device.getHamlet(), device.getSite()};
        // 找到用户权限层级的起始位置
        int startIndex = Arrays.asList(levels).indexOf(userLevel);
        DeviceTreeNode parentNode = null;
        // 从用户权限层级开始，构建到site层级的完整路径
        for (int i = startIndex; i < levels.length; i++) {
            if (StringUtils.isNotBlank(values[i])) {
                String nodeKey = levels[i] + ":" + values[i];
                // 检查节点是否已存在
                DeviceTreeNode currentNode = nodeMap.get(nodeKey);
                if (currentNode == null) {
                    // 创建新的地理位置节点
                    currentNode = new DeviceTreeNode(values[i], levels[i]);
                    nodeMap.put(nodeKey, currentNode);
                }
                // 建立父子关系
                if (parentNode != null) {
                    // 检查是否已经是子节点，避免重复添加
                    if (!parentNode.getChildren().contains(currentNode)) {
                        parentNode.addChild(currentNode);
                    }
                }
                parentNode = currentNode;
            }
        }
        // 在最后一个地理位置节点下添加设备节点
        if (parentNode != null) {
            DeviceTreeNode deviceNode = createDeviceNode(device);
            parentNode.addChild(deviceNode);
        }
    }

    /**
     * 找出根节点（用户权限层级对应的节点）
     */
    private List<DeviceTreeNode> findRootNodes(Map<String, DeviceTreeNode> nodeMap, String userLevel) {
        List<DeviceTreeNode> rootNodes = new ArrayList<>();
        // 根据用户权限层级，找出对应的根节点
        for (Map.Entry<String, DeviceTreeNode> entry : nodeMap.entrySet()) {
            String nodeKey = entry.getKey();
            DeviceTreeNode node = entry.getValue();
            // 如果节点的层级与用户权限层级匹配，则为根节点
            if (nodeKey.startsWith(userLevel + ":")) {
                rootNodes.add(node);
            }
        }
        return rootNodes;
    }

    /**
     * 创建设备节点
     */
    private DeviceTreeNode createDeviceNode(Device device) {
        DeviceTreeNode.DeviceInfo deviceInfo = new DeviceTreeNode.DeviceInfo();
        deviceInfo.setDeviceId(device.getId());
        deviceInfo.setDeviceName(device.getDeviceName());
        deviceInfo.setEquipmentNumber(device.getEquipmentNumber());
        deviceInfo.setState(device.getState());
        deviceInfo.setIp(device.getIp());
        deviceInfo.setStreamKey(device.getStreamKey());
        // 设备节点标签
        String deviceLabel = device.getDeviceName() + "(" + device.getIp() + ")";
        return new DeviceTreeNode(deviceLabel, deviceInfo);
    }
}
