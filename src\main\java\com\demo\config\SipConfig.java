package com.demo.config;

// import com.demo.service.GB28181SipServer;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
// import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
// import org.springframework.web.client.RestTemplate;

/**
 * SIP服务器配置类
 * 负责创建和配置SIP相关的Bean
 */
@Configuration
@EnableConfigurationProperties(GB28181Properties.class)
public class SipConfig {
    
    /**
     * 创建RestTemplate Bean
     * 用于调用ZLMediaKit的HTTP API
     * 注意：如果项目中已有RestTemplate Bean，请删除此方法
     */
    // @Bean
    // public RestTemplate restTemplate() {
    //     return new RestTemplate();
    // }
    
    /**
     * 创建GB28181 SIP服务器Bean
     * 注意：已通过@Component注解自动创建，此处不需要@Bean
     */
    // @Bean
    // public GB28181SipServer sipServer() {
    //     return new GB28181SipServer();
    // }
}
