spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  profiles:
#    active: prod
    active: dev

# 推流控制配置
stream:
  # 推流总开关，true时允许所有设备推流，false时按观看者逻辑
  tuiliu: true
  # 允许推流的时间段（支持精确时间格式）
  allowed-time:
    # 开始时间（支持格式：7 或 07:00:00）
    start: "07:00:00"
    # 结束时间（支持格式：19 或 19:00:00）
    end: "19:00:00"
# 使用 mybatis-plus 的配置
#mybatis-plus:
#  type-aliases-package: com.demo.entity
#  configuration:
#    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#  global-config:
#    db-config:
#      id-type: auto
#      logic-delete-field: deleted
#      logic-delete-value: 1
#      logic-not-delete-value: 0
# GB28181 SIP配置
gb28181:
  # 网络配置 - 统一管理所有IP
  network:
    # 服务器网络配置
    localIp: ***********        # 服务器本地内网IP
    publicIp: ***************     # 服务器公网IP
    # SIP网络配置
    sipBindIp: 0.0.0.0            # SIP服务绑定IP (0.0.0.0表示绑定所有接口)
    sipExternalIp: ***********  # SIP对外显示IP (Via/From头中使用)
    # 媒体网络配置
    mediaIp: ***************        # RTP媒体传输IP (SDP中使用)SDP中告诉摄像机推流到哪里
    zlmIp: ***************          # ZLMediaKit服务IP

  sip:
    port: 15060                   # SIP信令端口，GB28181标准端口
    domain: 5115250000            # SIP域，通常是行政区划码前10位
    id: 51152500002000000001      # SIP服务器ID，20位数字，符合GB28181规范
    password: ybda1234              # 设备注册密码，摄像机需要配置相同密码

  # ZLMediaKit流媒体服务器配置
  zlm:
    port: 81                    # ZLMediaKit HTTP API端口
    #    secret: MzoPms17tgVzWydI2pcTcmogdZ9pFavV  # ZLMediaKit API密钥FpvUDVyWlqohv98EEE5Of3UcFhc18Ipt
    secret: udgRe2yrs5aj4FVH4dE9P1Pd6Lbn2g0L  # ZLMediaKit API密钥FpvUDVyWlqohv98EEE5Of3UcFhc18Ipt
    rtp-port-start: 30000         # RTP端口范围起始值
    rtp-port-end: 30500            # RTP端口范围结束值，用于分配给摄像机推流


# HTTP-FLV:
# http://**************:88/rtp/34020000001320000001.flv
# HLS:
# http://**************:88/rtp/34020000001320000001/hls.m3u8
# RTSP:
# rtsp://**************:554/rtp/34020000001320000001
# RTMP:
# rtmp://**************:1936/rtp/34020000001320000001