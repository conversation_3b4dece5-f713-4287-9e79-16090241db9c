package com.demo.service;

import com.demo.config.StreamConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Set;
import java.util.HashSet;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

import static com.demo.controller.SrsCallbackController.SRS_MAP;

@Slf4j
@Service
public class StreamService {

    @Autowired
    private StreamConfig streamConfig;

    // 静态配置：永久允许推流的流名称列表
    private static final Set<String> PERMANENT_ALLOWED_STREAMS = new HashSet<String>() {{
//        add("c0742bfc6c031921680120"); // 您的流
//        add("c0742bfc6c031921680130");
        add("c0742bfc7b481921680130");
        add("c0742bfc7b481921680120");
        add("c0742bfc29f11921680130");
        add("c0742bfc29f11921680120");
        add("c0742bfc6c031921680120");
        add("c0742bfc6c031921680130");
        add("testsuite");
        add("34020000001320000001");
        // 可以在这里添加更多需要永久推流的流名称
    }};

    // 观看者计数：<streamName, viewerCount>
    private final ConcurrentHashMap<String, Integer> viewerCounts = new ConcurrentHashMap<>();

    // 推流权限状态：<streamName, isAllowed>
    private final ConcurrentHashMap<String, Boolean> publishPermissions = new ConcurrentHashMap<>();

    // 活跃推流记录：<streamName, 推流开始时间>
    private final ConcurrentHashMap<String, java.time.LocalDateTime> activeStreams = new ConcurrentHashMap<>();
    
    /**
     * 有观看者开始观看
     * @param streamName 流名称
     * @return 当前观看者数量
     */
    public synchronized int addViewer(String streamName) {
        int currentCount = viewerCounts.getOrDefault(streamName, 0);
        currentCount++;
        viewerCounts.put(streamName, currentCount);
        
        // 如果是第一个观看者，允许推流
        if (currentCount == 1) {
            publishPermissions.put(streamName, true);
            log.info("流 {} 首次有观看者，允许推流", SRS_MAP.get(streamName));
        }
        
        log.info("流 {} 增加观看者，当前观看者数: {}", SRS_MAP.get(streamName), currentCount);
        return currentCount;
    }
    
    /**
     * 观看者停止观看
     * @param streamName 流名称
     * @return 当前观看者数量
     */
    public synchronized int removeViewer(String streamName) {
        int currentCount = viewerCounts.getOrDefault(streamName, 0);
        if (currentCount > 0) {
            currentCount--;
            viewerCounts.put(streamName, currentCount);
        }
        
        // 如果没有观看者了，根据新的权限逻辑处理
        if (currentCount == 0) {
            boolean isInAllowedTime = streamConfig.isInAllowedTimeRange();
            boolean isTuiliuEnabled = streamConfig.getTuiliu();
            boolean isPermanentAllowed = PERMANENT_ALLOWED_STREAMS.contains(streamName);

            // 如果不在允许时间段内，直接取消推流权限
            if (!isInAllowedTime) {
                publishPermissions.put(streamName, false);
                log.info("流 {} 无观看者且不在允许推流时间段内，取消推流权限", SRS_MAP.get(streamName));
            }
            // 在允许时间段内
            else if (isTuiliuEnabled) {
                // TUILIU开启，保持推流权限
                log.info("流 {} 无观看者但TUILIU开关开启且在允许时间段内，保持推流权限", SRS_MAP.get(streamName));
            } else {
                // TUILIU关闭，按原逻辑处理
                if (!isPermanentAllowed) {
                    publishPermissions.put(streamName, false);
                    log.info("流 {} 无观看者且不在永久推流列表中，取消推流权限", SRS_MAP.get(streamName));
                } else {
                    log.info("流 {} 无观看者但在永久推流列表中，保持推流权限", SRS_MAP.get(streamName));
                }
            }
        }
        
        log.info("流 {} 减少观看者，当前观看者数: {}", SRS_MAP.get(streamName), currentCount);
        return currentCount;
    }
    
    /**
     * 检查是否允许推流
     * 新的权限控制逻辑：
     * 1. 如果TUILIU=true且在允许时间段内，允许所有设备推流
     * 2. 如果不在允许时间段内，即使TUILIU=true或在PERMANENT_ALLOWED_STREAMS中，无观看者也不允许推流
     * 3. 如果TUILIU=false，按原逻辑：PERMANENT_ALLOWED_STREAMS中的流可以无观看者推流，其他流需要有观看者
     * @param streamName 流名称
     * @return 是否允许推流
     */
    public boolean isPublishAllowed(String streamName) {
        boolean isInAllowedTime = streamConfig.isInAllowedTimeRange();
        boolean isTuiliuEnabled = streamConfig.getTuiliu();
        boolean isPermanentAllowed = PERMANENT_ALLOWED_STREAMS.contains(streamName);
        boolean hasViewers = getViewerCount(streamName) > 0;

        log.debug("推流权限检查 - 流: {}, 允许时间段: {}, TUILIU开关: {}, 永久允许: {}, 有观看者: {}",
                SRS_MAP.get(streamName), isInAllowedTime, isTuiliuEnabled, isPermanentAllowed, hasViewers);

        // 如果不在允许推流的时间段内
        if (!isInAllowedTime) {
            // 时间段外，只有有观看者才允许推流（无论其他条件如何）
            boolean allowed = hasViewers;
            if (!allowed) {
                log.info("流 {} 不在允许推流时间段内且无观看者，拒绝推流", SRS_MAP.get(streamName));
            }
            return allowed;
        }

        // 在允许推流的时间段内
        if (isTuiliuEnabled) {
            // TUILIU开关开启，允许所有设备推流
            log.debug("流 {} 在允许时间段内且TUILIU开关开启，允许推流", SRS_MAP.get(streamName));
            return true;
        } else {
            // TUILIU开关关闭，按原逻辑：永久允许列表中的流可以无观看者推流，其他流需要有观看者
            boolean allowed = isPermanentAllowed || hasViewers;
            if (!allowed) {
                log.info("流 {} TUILIU开关关闭，不在永久允许列表且无观看者，拒绝推流", SRS_MAP.get(streamName));
            }
            return allowed;
        }
    }
    
    /**
     * 获取当前观看者数量
     * @param streamName 流名称
     * @return 观看者数量
     */
    public int getViewerCount(String streamName) {
        return viewerCounts.getOrDefault(streamName, 0);
    }
    
    /**
     * 手动设置推流权限（用于特殊情况）
     * @param streamName 流名称
     * @param allowed 是否允许
     */
    public void setPublishPermission(String streamName, boolean allowed) {
        publishPermissions.put(streamName, allowed);
        log.info("手动设置流 {} 推流权限: {}", SRS_MAP.get(streamName), allowed);
    }
    
    /**
     * 推流结束时清理状态
     * @param streamName 流名称
     */
    public synchronized void onPublishStop(String streamName) {
        boolean isInAllowedTime = streamConfig.isInAllowedTimeRange();
        boolean isTuiliuEnabled = streamConfig.getTuiliu();
        boolean isPermanentAllowed = PERMANENT_ALLOWED_STREAMS.contains(streamName);

        // 根据新的权限逻辑决定是否清理权限状态
        // 如果不在允许时间段内，总是清理权限状态
        if (!isInAllowedTime) {
            publishPermissions.remove(streamName);
            log.info("流 {} 推流结束且不在允许时间段内，清理推流权限", SRS_MAP.get(streamName));
        }
        // 在允许时间段内
        else if (isTuiliuEnabled) {
            // TUILIU开启，保留推流权限
            log.info("流 {} 推流结束但TUILIU开关开启且在允许时间段内，保留推流权限", SRS_MAP.get(streamName));
        } else {
            // TUILIU关闭，按原逻辑处理
            if (!isPermanentAllowed) {
                publishPermissions.remove(streamName);
                log.info("流 {} 推流结束且不在永久推流列表中，清理推流权限", SRS_MAP.get(streamName));
            } else {
                log.info("流 {} 推流结束但在永久推流列表中，保留推流权限", SRS_MAP.get(streamName));
            }
        }

        // 观看者计数总是清理
        viewerCounts.remove(streamName);
        // 清理活跃推流记录
        activeStreams.remove(streamName);
        log.info("流 {} 推流结束，清理观看者计数和活跃推流记录", SRS_MAP.get(streamName));
    }
    


    /**
     * 检查流是否在永久允许列表中
     * @param streamName 流名称
     * @return 是否永久允许
     */
    public boolean isPermanentAllowed(String streamName) {
        return PERMANENT_ALLOWED_STREAMS.contains(streamName);
    }

    /**
     * 记录推流开始
     * @param streamName 流名称
     */
    public synchronized void onPublishStart(String streamName) {
        activeStreams.put(streamName, java.time.LocalDateTime.now());
        log.debug("记录推流开始 - 流: {}, 开始时间: {}", SRS_MAP.get(streamName), java.time.LocalDateTime.now());
    }

    /**
     * 获取所有活跃的推流
     * @return 活跃推流映射 <streamName, 开始时间>
     */
    public ConcurrentHashMap<String, java.time.LocalDateTime> getActiveStreams() {
        return new ConcurrentHashMap<>(activeStreams);
    }

    /**
     * 检查并获取超时的推流列表
     * @return 超时的流名称列表
     */
    public List<String> getTimeoutStreams() {
        List<String> timeoutStreams = new ArrayList<>();
        boolean isInAllowedTime = streamConfig.isInAllowedTimeRange();

        // 如果当前在允许时间段内，没有超时流
        if (isInAllowedTime) {
            return timeoutStreams;
        }

        // 检查所有活跃推流
        for (Map.Entry<String, java.time.LocalDateTime> entry : activeStreams.entrySet()) {
            String streamName = entry.getKey();
            boolean hasViewers = getViewerCount(streamName) > 0;

            // 如果不在允许时间段内且无观看者，则认为超时
            if (!hasViewers) {
                timeoutStreams.add(streamName);
                log.info("发现超时推流 - 流: {}, 开始时间: {}, 当前无观看者且不在允许时间段",
                        SRS_MAP.get(streamName), entry.getValue());
            }
        }

        return timeoutStreams;
    }

    /**
     * 获取所有流的状态信息（用于监控）
     * @return 流状态映射
     */
    public ConcurrentHashMap<String, Object> getAllStreamStatus() {
        ConcurrentHashMap<String, Object> status = new ConcurrentHashMap<>();

        // 添加所有有权限的流（包括永久和临时）
        Set<String> allStreams = new HashSet<>(publishPermissions.keySet());
        allStreams.addAll(PERMANENT_ALLOWED_STREAMS);
        allStreams.addAll(activeStreams.keySet());

        for (String streamName : allStreams) {
            ConcurrentHashMap<String, Object> streamInfo = new ConcurrentHashMap<>();
            streamInfo.put("viewerCount", getViewerCount(streamName));
            streamInfo.put("publishAllowed", isPublishAllowed(streamName));
            streamInfo.put("permanentAllowed", isPermanentAllowed(streamName));
            streamInfo.put("isActive", activeStreams.containsKey(streamName));
            streamInfo.put("startTime", activeStreams.get(streamName));
            status.put(streamName, streamInfo);
        }

        return status;
    }
    

}
